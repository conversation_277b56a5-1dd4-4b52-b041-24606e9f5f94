export type TAlertConfig = {
    id: string;
    symbol?: string;
    timeframe?: string;
    enabled: boolean;
    triggerType: string;
    chart?: string;
    muted: boolean;
    general: boolean;
    arg1: TAlertArg;
    arg2?: TAlertArg;
    op: TAlertOp;
}

export type TAlertConfigNew = Omit<TAlertConfig, 'id'>;
export type TAlertOp = { name: string, type: string };
export type TAlertTriggerType = { name: string, type: string };
export type TAlertArg = {
    type: string;
    scriptName?: string;
    name: string;
    refId: string;
    value?: number;
}