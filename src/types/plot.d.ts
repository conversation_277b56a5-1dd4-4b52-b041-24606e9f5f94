type EPlot = "line" | "candlestick" | "histogram";

export type TChartData = {
    plots: TPlotData[];
    name: string,
    ordering: number;

}

export interface TBasePlotData {
    type: EPlot;
    name: string;
    id: string;
    config?: Record<string, any>;
}

export type TPlotData = TBasePlotData &
    (
        | { type: "line"; data: TDataPoint[] }
        | { type: "histogram"; data: TDataPoint[] }
        | { type: "candlestick"; data: TCandlestick[] }
        | { type: "priceLine"; data: number }
        | { type: "scatter"; data: TScatter[] }
    )

export interface TDataPoint {
    time: number;
    value: number | null | undefined;
    color?: string;
}

export interface TCandlestick {
    open: number;
    close: number;
    high: number;
    low: number;
    time: number;
    color?: string;
}

export interface TScatter {
    time: number;
    value: number;
    color?: string;
    shape: string;
    size: number;
    position: string;
}

export interface TTradeDist {
    timeRange: TRange;
    data: number[];
}


export type TStreamPlotData = {
    type: string;
    id: string;
    name: string;
    data: any;
}
