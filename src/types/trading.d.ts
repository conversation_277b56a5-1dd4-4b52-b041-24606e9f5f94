export interface TPosition {
    symbol: string;
    side: string;
    cryptoAmount: string;
    usdAmount: string;
    unrealizedPnl: string;
}

export interface TMarketOrderResponse {
    orderId: string;
    cryptoAmount: string;
    symbol: string;
    side: string;

}

export interface TClosePositionResponse {
    orderId: string;
    symbol: string;
    side: string;
}

export interface TMarketOrderParams {
    symbol: string;
    side: string;
    usdAmount: number;
}

export enum EPositionSide {
    Long = 'long',
    Short = 'short'
}