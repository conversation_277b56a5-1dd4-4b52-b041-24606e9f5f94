
import type { TChartData, TPlotData, TStreamPlotData } from "../types/plot";
import { CandlestickStore } from "./candlestickStore";
import { HistogramStore } from "./histogramStore";
import { LineStore } from "./lineStore";

export abstract class ChartStore {
    readonly name: string;
    readonly plotStoreMap: Map<string, LineStore | CandlestickStore | HistogramStore>;
    private mainPlotId?: string;


    constructor(data: TChartData) {
        this.name = data.name
        this.plotStoreMap = new Map()

        for (const plot of data.plots) {
            this.plotStoreMap.set(plot.id, this.createPlotStore(plot))
            if (plot.config?.isMain) {
                this.mainPlotId = plot.id
            }
        }
    }

    updateData(data: TStreamPlotData) {
        const plotData = this.plotStoreMap.get(data.id)
        switch (plotData?.type) {
            case 'line':
            case 'candlestick':
            case 'histogram':
                plotData?.update(data.data[0])
                return true
            default:
                return false
        }

    }

    get mainPlotStore() {
        if (!this.mainPlotId) return
        return this.plotStoreMap.get(this.mainPlotId)
    }

    appendData(data: TChartData) {
        for (const plot of data.plots) {
            const plotData = this.plotStoreMap.get(plot.id)
            switch (plot.type) {
                case 'line':
                case 'candlestick':
                case 'histogram':
                    plotData?.append(plot.data as any)
                    break;

                default:
                    break;
            }
        }
    }

    prependData(data: TChartData) {
        for (const plot of data.plots) {
            const plotData = this.plotStoreMap.get(plot.id)
            switch (plot.type) {
                case 'line':
                case 'candlestick':
                case 'histogram':
                    plotData?.prepend(plot.data as any)
                    break;
                default:
                    break;
            }
        }
    }

    createPlotStore(plot: TPlotData) {
        switch (plot.type) {
            case 'line':
                return new LineStore(plot.id, plot.data, plot.config)
            case 'candlestick':
                return new CandlestickStore(plot.id, plot.data, plot.config)
            case 'histogram':
                return new HistogramStore(plot.id, plot.data, plot.config)
            default:
                throw new Error('invalid plot type')
        }
    }

    getTimeRange() {
        let max = -Infinity;
        let min = Infinity;

        this.plotStoreMap.forEach((plotData) => {
            if (plotData.length === 0) return
            if (plotData.last.time > max) max = plotData.last.time
            if (plotData.first.time < min) min = plotData.first.time
        })
        if (max === -Infinity || min === Infinity) return
        return {
            max,
            min
        }
    }

    getValueRangeByTime(from: number, to: number) {
        let max = -Infinity;
        let min = Infinity;

        this.plotStoreMap.forEach((plotData) => {
            const range = plotData.getValueRangeByTime(from, to)
            if (range) {
                if (range.max > max) max = range.max
                if (range.min < min) min = range.min
            }
        })
        if (max === -Infinity || min === Infinity) return
        return {
            max,
            min
        }
    }
    get plotStoreList() {
        return Array.from(this.plotStoreMap.values())
    }
    abstract get priceLabelValue(): number
}