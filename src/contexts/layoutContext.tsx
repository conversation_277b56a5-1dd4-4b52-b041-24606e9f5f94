import { XAXIS_HEIGHT, YAXIS_WIDTH } from "../commons/constants";
import { useDataContext } from "./dataContext";
import { debounce } from "es-toolkit";
import { createContext, ReactNode, useContext, useEffect, useState } from "react";


type TLayoutInfo = {
    width: number;
    height: number;
    chartGroupHeight: number;
    xAxisWidth: number;
    chartLayoutInfoMap: Map<string, { height: number, top: number }>
}

type TLayoutContext = {
    layoutInfo?: TLayoutInfo
}


export const LayoutContext = createContext<TLayoutContext | undefined>(undefined);

export const useLayoutContext = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayoutContext must be used within an LayoutContextProvider');
    }
    return context;
}

export const LayoutContextProvider = (props: { children: ReactNode, container: React.RefObject<HTMLDivElement | null> }) => {
    console.debug('rendering layout context provider')
    const [layoutInfo, setLayoutInfo] = useState<TLayoutInfo>()
    const { chartStoreMap } = useDataContext()
    const chartCount = chartStoreMap?.size ?? 0

    const handleResize = debounce(() => {
        if (!chartStoreMap) return
        if (!props.container.current) return
        const totalFlex = 4 + (chartCount - 1);
        const chartLayoutInfoMap = new Map()
        const availHeight = props.container.current.clientHeight - XAXIS_HEIGHT;
        const storeList = Array.from(chartStoreMap.values())
        for (let i = 0; i < chartCount; i++) {
            const flex = i === 0 ? 4 : 1;
            const chartHeight = (flex / totalFlex) * availHeight;
            const top = i === 0 ? 0 : ((4 + i - 1) / totalFlex) * availHeight;
            chartLayoutInfoMap.set(storeList[i].name, { height: chartHeight, top })
        }

        setLayoutInfo({
            width: props.container.current.clientWidth,
            height: props.container.current.clientHeight,
            chartGroupHeight: availHeight,
            chartLayoutInfoMap,
            xAxisWidth: props.container.current.clientWidth - YAXIS_WIDTH,
        })
    }, 100)

    useEffect(() => {
        if (!props.container.current || !chartStoreMap?.size) return

        const obs = new ResizeObserver(handleResize);

        obs.observe(props.container.current);

        return () => {
            if (props.container.current) {
                obs.unobserve(props.container.current);
            }
            obs.disconnect();
        };
    }, [props.container, chartStoreMap]);

    return (
        <LayoutContext.Provider value={{ layoutInfo }}>
            {props.children}
        </LayoutContext.Provider>
    );
}

