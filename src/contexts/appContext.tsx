import { timeframe2timeUnit } from '../commons/util';
import { TSignal, useSignal } from '../hooks/useSignal';
import type { TCoord, TRange } from '../types/common.d';
import { createContext, ReactNode, useContext, useMemo } from 'react';


type TAppContext = {
    timeUnit: number;
    symbol: string;
    timeframe: string;
    activeChart: TSignal<string>
    crosshairsCoord: TSignal<TCoord | null>
    activeTool: TSignal<string>
    timeRange: TSignal<TRange>
};
export const AppContext = createContext<TAppContext | undefined>(undefined);

export const useAppContext = () => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useAppContext must be used within an AppContextProvider');
    }
    return context;
}

export const AppContextProvider = (props: { children: ReactNode, symbol: string, timeframe: string, defaultTimeRange?: TRange }) => {
    console.debug('rendering app context provider')
    const timeUnit = timeframe2timeUnit(props.timeframe);
    const activeChart = useSignal('');
    const activeTool = useSignal('');
    const crosshairsCoord = useSignal<TCoord | null>(null);
    const timeRange = useSignal<TRange>(props.defaultTimeRange ?? {
        max: Date.now(),
        min: Date.now() - timeUnit * 100
    });

    const value: TAppContext = useMemo(() => ({
        timeUnit,
        symbol: props.symbol,
        timeframe: props.timeframe,
        activeChart,
        crosshairsCoord,
        activeTool,
        timeRange,
    }), [props.symbol, props.timeframe, timeUnit, activeChart, activeTool, crosshairsCoord, timeRange]);

    return (
        <AppContext.Provider value={value}>
            {props.children}
        </AppContext.Provider>
    );
}
