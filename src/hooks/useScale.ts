
import { useAppContext } from "../contexts/appContext";
import { useChartContext } from "../contexts/chartContext";

import { TRange } from "../types/common";

export const usePaneScale = () => {
    console.debug('use pane scale')
    const { timeRange } = useAppContext()
    const { valueRange, paneWidth, height } = useChartContext()

    return useScale({
        width: paneWidth,
        height: height,
        valueRange: valueRange.value,
        timeRange: timeRange.value
    })
}

export const useYAxisScale = () => {
    console.debug('use y axis scale')
    const { valueRange, yAxisWidth, height } = useChartContext()

    return useScale({
        width: yAxisWidth,
        height: height,
        valueRange: valueRange.value,
        timeRange: { min: 0, max: yAxisWidth }
    })
}


export const useScale = (props: { width: number, height: number, valueRange: TRange, timeRange: TRange }) => {
    console.debug('use scale')
    const { width, height, valueRange, timeRange } = props
    const valueSpan = valueRange.max - valueRange.min
    const timeSpan = timeRange.max - timeRange.min

    const v2y = (d: number) => {
        if (!valueRange || !height) return NaN;
        if (valueSpan <= 0) return NaN;
        // Invert Y-axis (0 at top, height at bottom)
        return height - ((d - valueRange.min) / valueSpan * height);
    }

    const y2v = (c: number) => {
        if (!valueRange || !height) return NaN;
        if (valueSpan <= 0) return NaN;
        // Invert Y-axis (0 at top, height at bottom)
        return valueRange.min + (valueSpan * (height - c) / height);
    }

    const t2x = (d: number) => {
        if (!timeRange || !width) return NaN;
        if (timeSpan <= 0) return 0;
        return ((d - timeRange.min) / timeSpan) * width;
    }

    const x2t = (c: number) => {
        if (!timeRange || !width) return NaN;
        if (timeSpan <= 0) return 0;
        return timeRange.min + (timeSpan * c / width);
    }

    const deltaX2t = (deltaX: number) => {
        if (!timeRange || !width) return NaN;
        if (timeSpan <= 0) return 0;
        return timeSpan * deltaX / width;
    }

    const deltaY2v = (deltaY: number) => {
        if (!valueRange || !height) return NaN;
        if (valueSpan <= 0) return NaN;
        return valueSpan * deltaY / height;
    }

    const deltaT2x = (deltaT: number) => {
        if (!timeRange || !width) return NaN;
        if (timeSpan <= 0) return NaN;
        return (deltaT / timeSpan) * width;
    }

    const deltaV2y = (deltaV: number) => {
        if (!valueRange || !height) return NaN;
        if (valueSpan <= 0) return NaN;
        return (deltaV / valueSpan) * height;
    }

    return {
        v2y,
        y2v,
        t2x,
        x2t,
        deltaX2t,
        deltaY2v,
        deltaT2x,
        deltaV2y,
    }
}