import { useSyncExternalStore, useRef, useMemo } from 'react';

export type TSignal<T> = { value: T } & { use(): T };

/**
 * Reactive signal for React. Components only re-render when they invoke `signal.use()`
 * to read the value.
 */
export function useSignal<T>(initial: T): TSignal<T> {
    console.debug('use signal')
    // Internal store: holds the current value + subscriber callbacks
    const storeRef = useRef<{ value: T; subs: Set<() => void> }>({
        value: initial,
        subs: new Set(),
    });

    // Create a stable signal object once
    const signal = useMemo<TSignal<T>>(() => ({
        // Direct getter/setter operate on the store without subscribing
        get value() {
            return storeRef.current.value;
        },
        set value(v: T) {
            if (storeRef.current.value !== v) {
                storeRef.current.value = v;
                // Notify all subscribers
                for (const cb of storeRef.current.subs) cb();
            }
        },

        // React hook: subscribe and return current snapshot
        use() {
            const subscribe = (cb: () => void) => {
                storeRef.current.subs.add(cb);
                return () => {
                    storeRef.current.subs.delete(cb);
                };
            };

            const getSnapshot = () => storeRef.current.value;

            // Subscribe only when this hook is invoked
            return useSyncExternalStore(subscribe, getSnapshot, getSnapshot);
        }
    }), []);

    return signal;
}