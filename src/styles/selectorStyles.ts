import { styled } from '@stitches/react';
import { LiquidGlass } from './commonStyles';

export const SelectorContainer = styled('div', {
    position: 'relative',
    zIndex: 1000,
    pointerEvents: 'auto', // Ensure selectors still receive mouse events
    // border: '1px solid rgba(7, 81, 207, 0.3)',
    // padding: '10px',
    transition: 'all 0.2s ease',
    // minWidth: '80px',
    width: 'auto',

    variants: {
        disabled: {
            true: {
                opacity: 0.5,
                cursor: 'not-allowed',
            }
        }
    }
});

export const SelectorHeader = styled('div', LiquidGlass, {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
    padding: '10px',
});

export const SelectorTitle = styled('div', {
    color: 'rgb(7, 81, 207)',
    fontSize: '17px',
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    lineHeight: '1',
});

export const SelectorDropdownIcon = styled('div', {
    color: 'rgb(7, 81, 207)',
    fontSize: '10px',
    transition: 'transform 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '8px',

    variants: {
        open: {
            true: {
                transform: 'rotate(180deg)',
            }
        }
    }
});

export const SelectorDropdown = styled('div', LiquidGlass, {
    position: 'absolute',
    top: '100%',
    left: 0,
    width: '100%',
    zIndex: 999,
    transition: 'all 0.2s ease',
    marginTop: '5px',
});

export const SelectorList = styled('div', {
    display: 'flex',
    flexDirection: 'column',
    maxHeight: '200px',
    overflowY: 'auto',
    gap: '2px',
    padding: '5px',

    // Scrollbar styling
    '&::-webkit-scrollbar': {
        display: 'none',
    },
    '&::-webkit-scrollbar-track': {
        background: 'rgba(7, 81, 207, 0.05)',
        borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: 'rgba(7, 81, 207, 0.3)',
        borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
        background: 'rgba(7, 81, 207, 0.5)',
    },
});

export const SelectorItem = styled('div', {
    padding: '6px 8px',
    fontSize: '14px',
    color: 'rgba(7, 81, 207)',
    cursor: 'pointer',
    borderRadius: '6px', // Even smaller border radius for items
    transition: 'all 0.1s ease',
    display: 'flex',
    alignItems: 'center',
    lineHeight: '1.2',
    height: '28px',
    fontWeight: 'bold',

    '&:hover': {
        backgroundColor: 'rgba(7, 81, 207, 0.1)',
        color: 'rgb(7, 81, 207)',
    },

    variants: {
        selected: {
            true: {
                backgroundColor: 'rgba(7, 81, 207, 0.2)',
                color: 'rgb(7, 81, 207)',
                fontWeight: 'bold',
            }
        }
    }
});
