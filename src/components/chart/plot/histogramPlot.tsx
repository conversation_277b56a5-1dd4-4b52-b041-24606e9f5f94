import { Shape } from "react-konva";
import { type HistogramStore } from "../../../store/histogramStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";
import { isNil } from 'es-toolkit';

export function HistogramPlot(props: { store: HistogramStore }) {
    console.debug('rendering histogram plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x, deltaT2x } = usePaneScale()

    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)
        let barWidth = deltaT2x(timeUnit) - (store.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);
        const zeroY = v2y(0); // Baseline for histogram

        for (const dataPoint of list) {
            if (isNil(dataPoint.value)) continue;

            const x = t2x(dataPoint.time);
            const valueY = v2y(dataPoint.value);
            const isPositive = dataPoint.value > 0;

            // Calculate bar dimensions
            const barX = x - barWidth / 2;
            const barY = isPositive ? valueY : zeroY;
            const barHeight = Math.abs(valueY - zeroY);
            const minHeightForRoundCorners = cornerRadius * 2;
            if (isNaN(zeroY)) continue;

            // Determine color
            const color = dataPoint.color || (isPositive ? store.config?.riseColor || '#26a69a' : store.config?.fallColor || '#ef5350');

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, zeroY);
                context.lineTo(x, valueY);
                context.closePath();
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Create gradient for histogram bars
                const gradient = context.createLinearGradient(
                    barX,
                    barY,           // Start from top of bar
                    barX,
                    barY + barHeight  // End at bottom of bar
                );

                // Add gradient stops with opacity variation
                if (isPositive) {
                    // Positive: gradient from bottom (opaque) to top (transparent)
                    gradient.addColorStop(0, color + '80'); // 50% opacity at top
                    gradient.addColorStop(1, color + 'FF'); // Full opacity at bottom
                } else {
                    // Negative: gradient from top (opaque) to bottom (transparent)
                    gradient.addColorStop(0, color + 'FF'); // Full opacity at top
                    gradient.addColorStop(1, color + '80'); // 50% opacity at bottom
                }

                // Draw bar with rounded corners if large enough
                context.beginPath();
                if (barHeight < minHeightForRoundCorners) {
                    // For small bars, use simple rectangle
                    context.rect(barX, barY, barWidth, barHeight);
                } else {
                    // For larger bars, use rounded corners
                    context.moveTo(barX + cornerRadius, barY);
                    context.lineTo(barX + barWidth - cornerRadius, barY);
                    context.arcTo(barX + barWidth, barY, barX + barWidth, barY + cornerRadius, cornerRadius);
                    context.lineTo(barX + barWidth, barY + barHeight - cornerRadius);
                    context.arcTo(barX + barWidth, barY + barHeight, barX + barWidth - cornerRadius, barY + barHeight, cornerRadius);
                    context.lineTo(barX + cornerRadius, barY + barHeight);
                    context.arcTo(barX, barY + barHeight, barX, barY + barHeight - cornerRadius, cornerRadius);
                    context.lineTo(barX, barY + cornerRadius);
                    context.arcTo(barX, barY, barX + cornerRadius, barY, cornerRadius);
                }
                context.closePath();

                context.setAttr('fillStyle', gradient);
                context.fill();
            }
        }
    }

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}
