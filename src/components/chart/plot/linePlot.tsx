import { Shape } from "react-konva";
import { type LineStore } from "../../../store/lineStore";
import { usePaneScale } from "../../../hooks/useScale";
import Konva from "konva";
import { useAppContext } from "../../../contexts/appContext";
import { useChartContext } from "../../../contexts/chartContext";
import { useDataContext } from "../../../contexts/dataContext";
import { isNil } from 'es-toolkit';

export function LinePlot(props: { store: LineStore }) {
    console.debug('rendering line plot')
    const { store } = props
    const { timeRange } = useAppContext()
    const { valueRange } = useChartContext()
    const { ticker } = useDataContext()
    timeRange.use()
    valueRange.use()
    ticker.use()

    const { v2y, t2x } = usePaneScale()

    const sceneFunc = (context: Konva.Context) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max)

        if (list.length === 0) return;

        // Default line color
        const defaultColor = store.config?.color || '#2196f3';
        const lineWidth = store.config?.lineWidth || 2;

        context.setAttr('lineWidth', lineWidth);
        context.setAttr('lineCap', 'round');
        context.setAttr('lineJoin', 'round');

        let isDrawing = false;
        let lastValidPoint: { x: number, y: number } | null = null;

        for (let i = 0; i < list.length; i++) {
            const dataPoint = list[i];

            if (isNil(dataPoint.value)) {
                // Handle null/undefined values by ending current path
                if (isDrawing) {
                    context.stroke();
                    isDrawing = false;
                }
                lastValidPoint = null;
                continue;
            }

            const x = t2x(dataPoint.time);
            const y = v2y(dataPoint.value);
            const color = dataPoint.color || defaultColor;

            if (!isDrawing) {
                // Start new path
                context.beginPath();
                context.setAttr('strokeStyle', color);
                context.moveTo(x, y);
                isDrawing = true;
                lastValidPoint = { x, y };
            } else {
                // Continue path, but check if color changed
                const lastColor = list[i - 1]?.color || defaultColor;
                if (color !== lastColor && lastValidPoint) {
                    // Color changed, end current path and start new one
                    context.stroke();
                    context.beginPath();
                    context.setAttr('strokeStyle', color);
                    context.moveTo(lastValidPoint.x, lastValidPoint.y);
                }

                context.lineTo(x, y);
                lastValidPoint = { x, y };
            }
        }

        // Finish the last path if we were drawing
        if (isDrawing) {
            context.stroke();
        }
    }

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}
