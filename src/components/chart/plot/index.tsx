import { CandlestickStore } from "../../../store/candlestickStore"
import { HistogramStore } from "../../../store/histogramStore"
import { LineStore } from "../../../store/lineStore"
import { CandlestickPlot } from "./candlestickPlot"
import { HistogramPlot } from "./histogramPlot"
import { LinePlot } from "./linePlot"

export const Plot = (props: { store: CandlestickStore | LineStore | HistogramStore }) => {
    console.debug('rendering plot')

    switch (props.store.type) {
        case 'candlestick':
            return <CandlestickPlot store={props.store as CandlestickStore} />
        case 'line':
            return <LinePlot store={props.store as LineStore} />
        case 'histogram':
            return <HistogramPlot store={props.store as HistogramStore} />
        default:
            return null
    }
}