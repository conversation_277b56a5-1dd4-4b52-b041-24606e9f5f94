import { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>or<PERSON><PERSON><PERSON>, SelectorHeader, SelectorTitle, SelectorDropdownIcon, SelectorList, SelectorItem, SelectorDropdown } from '../styles/selectorStyles';


export const Selector = (props: {
    value: string,
    options: { label: string, value: string }[],
    onSelectChange: (value: string) => void,
    enableKeyboardNavigation?: boolean,
    disabled?: boolean,
    customKeys?: {
        up?: string[],
        down?: string[]
    }
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    const toggleDropdown = () => {
        if (props.disabled) return;
        setIsOpen(!isOpen);
    };

    const handleSelect = (value: string) => {
        if (props.disabled) return;
        props.onSelectChange(value);
        setIsOpen(false);
    };

    // Find the current index in options array
    const currentIndex = props.options.findIndex(item => item.value === props.value);

    // <PERSON>le keyboard navigation
    const navigateWithKeyboard = useCallback((direction: 'up' | 'down') => {
        if (props.options.length === 0 || props.disabled) return;

        let newIndex;
        if (direction === 'up') {
            newIndex = currentIndex > 0 ? currentIndex - 1 : props.options.length - 1;
        } else {
            newIndex = currentIndex < props.options.length - 1 ? currentIndex + 1 : 0;
        }

        props.onSelectChange(props.options[newIndex].value);
    }, [props.options, props.onSelectChange, currentIndex, props.disabled]);

    // Handle keyboard events
    useEffect(() => {
        // Skip if keyboard navigation is not enabled
        if (props.enableKeyboardNavigation !== true) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            // Only handle if not in an input field
            if (document.activeElement?.tagName === 'INPUT' ||
                document.activeElement?.tagName === 'TEXTAREA' ||
                document.activeElement?.tagName === 'SELECT') {
                return;
            }

            // Check if we should use custom keys or default arrow keys
            if (props.customKeys) {
                // Use only custom keys if provided
                const upKeys = props.customKeys.up || [];
                const downKeys = props.customKeys.down || [];

                if (upKeys.includes(e.key)) {
                    e.preventDefault();
                    navigateWithKeyboard('up');
                } else if (downKeys.includes(e.key)) {
                    e.preventDefault();
                    navigateWithKeyboard('down');
                }
            } else {
                // Use default arrow keys only if no custom keys are provided
                if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    navigateWithKeyboard('up');
                } else if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    navigateWithKeyboard('down');
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [navigateWithKeyboard, props.enableKeyboardNavigation, props.customKeys]);

    // Handle clicks outside the dropdown
    useEffect(() => {
        if (!isOpen) return;

        const handleClickOutside = (event: MouseEvent) => {
            if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        // Add event listener when dropdown is open
        document.addEventListener('click', handleClickOutside);

        // Clean up the event listener when component unmounts or dropdown closes
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [isOpen]);

    return (
        <SelectorContainer ref={containerRef} disabled={props.disabled}>
            <SelectorHeader onClick={toggleDropdown}>
                <SelectorTitle>
                    {props.options.find(item => item.value === props.value)?.label ?? ''}
                </SelectorTitle>
                <SelectorDropdownIcon open={isOpen}>▼</SelectorDropdownIcon>
            </SelectorHeader>

            {isOpen && !props.disabled && (
                <SelectorDropdown>
                    <SelectorList>
                        {props.options.map(item => (
                            <SelectorItem
                                key={item.value}
                                selected={item.value === props.value}
                                onClick={() => handleSelect(item.value)}
                            >
                                {item.label}
                            </SelectorItem>
                        ))}
                    </SelectorList>
                </SelectorDropdown>
            )}
        </SelectorContainer>
    );
};
