import { Layer, Stage } from 'react-konva'
import { XAXIS_HEIGHT } from '../commons/constants'
import { useLayoutContext } from '../contexts/layoutContext'
import Konva from 'konva'
import { useAppContext } from '../contexts/appContext'

export const XAxis = () => {
    console.debug('rendering x axis')
    const { layoutInfo } = useLayoutContext()
    const { crosshairsCoord, activeChart } = useAppContext()

    return (
        layoutInfo &&
        <Stage
            width={layoutInfo.xAxisWidth}
            height={XAXIS_HEIGHT}
            onMouseEnter={onMouseEnter}
        >
            <Layer>
            </Layer>
        </Stage>
    )


    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsCoord.value = null
        activeChart.value = ''
    }
}
