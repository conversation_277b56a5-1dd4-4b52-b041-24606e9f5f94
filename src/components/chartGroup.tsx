import { useDataContext } from "../contexts/dataContext";
import { useLayoutContext } from "../contexts/layoutContext";
import { ChartGroupContainer } from "../styles/chartStyles";
import { Chart } from "./chart/chart";


export const ChartGroup = () => {
    console.debug('rendering chart group')
    const { layoutInfo } = useLayoutContext()
    const { chartStoreMap } = useDataContext()

    return (
        layoutInfo && chartStoreMap &&
        <ChartGroupContainer>
            {Array.from(chartStoreMap.values()).map(chartStore => {
                const chartLayoutInfo = layoutInfo.chartLayoutInfoMap.get(chartStore.name)
                if (!chartLayoutInfo) return
                return (
                    <Chart
                        store={chartStore}
                        key={chartStore.name}
                        width={layoutInfo.width}
                        height={chartLayoutInfo.height}
                        style={{
                            position: 'absolute',
                            height: chartLayoutInfo.height,
                            top: chartLayoutInfo.top,
                        }}
                    />
                )
            })}
        </ChartGroupContainer>
    );
}