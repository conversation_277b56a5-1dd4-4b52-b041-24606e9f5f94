import type { TAlertConfigNew } from "../types/alert"
import type { TDataPoint } from "../types/common"
import type { TPosition } from "../types/trading"

const apiHost = 'http://localhost:5090'

export function fetchSymbolList() {
    return fetch(`${apiHost}/symbol`)
        .then(res => res.json())
}
export function fetchTimeframeList() {
    return fetch(`${apiHost}/timeframe`)
        .then(res => res.json())
}

export function createDrawingConfig(params: { symbol: string, chart: string, type: string, dataPoints: TDataPoint[] }) {
    return fetch(`${apiHost}/drawing-config`, {
        method: 'POST',
        body: JSON.stringify(params),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function fetchDrawingConfigs(params: { symbol: string, chart?: string }) {
    return fetch(`${apiHost}/drawing-config?symbol=${params.symbol}&chart=${params.chart ?? ''}`, {
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}


export function deleteDrawingConfig(id: string) {
    return fetch(`${apiHost}/drawing-config/${id}`, {
        method: 'DELETE',
        body: JSON.stringify({}),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function updateDrawingConfig(id: string, dataPoints: TDataPoint[]) {
    return fetch(`${apiHost}/drawing-config/${id}`, {
        method: 'PUT',
        body: JSON.stringify({ dataPoints }),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function clearDrawingConfigs(symbol: string) {
    return fetch(`${apiHost}/drawing-config?symbol=${symbol}`, {
        method: 'DELETE'
    })
        .then(res => res.json())
}



export function fetchTradeDist(params: { symbol: string, timeframe: string, startTime: number, endTime: number }) {
    return fetch(`${apiHost}/trade-dist?symbol=${params.symbol}&timeframe=${params.timeframe}&startTime=${params.startTime}&endTime=${params.endTime}`)
        .then(res => res.json())
}

export function fetchAlertTriggerTypeList() {
    return fetch(`${apiHost}/alert-trigger-type`)
        .then(res => res.json())
}

export function createAlertConfig(params: TAlertConfigNew) {
    return fetch(`${apiHost}/alert-config`, {
        method: 'POST',
        body: JSON.stringify(params),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function fetchAlertConfigs() {
    return fetch(`${apiHost}/alert-config`)
        .then(res => res.json())
}

export function deleteAlertConfig(id: string) {
    return fetch(`${apiHost}/alert-config/${id}`, { method: 'DELETE' })
        .then(res => res.json())
}

export function placeMarketOrder(params: { symbol: string, side: string, usdAmount: number }): Promise<{ orderId: string, cryptoAmount: string, symbol: string, side: string }> {
    return fetch(`${apiHost}/market-order`, {
        method: 'POST',
        body: JSON.stringify(params),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function fetchPositionList(): Promise<{ symbol: string, side: string, cryptoAmount: string, usdAmount: string }[]> {
    return fetch(`${apiHost}/position`, {
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function fetchAccount(): Promise<{ availableMargin: string, positions: TPosition[] }> {
    return fetch(`${apiHost}/account`, {
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}

export function closePosition(symbol: string): Promise<{ orderId: string, symbol: string, side: string }> {
    return fetch(`${apiHost}/position/close`, {
        body: JSON.stringify({ symbol }),
        headers: {
            'Content-Type': 'application/json',
        },
        method: 'POST'
    })
        .then(res => res.json())
}

export function savePushSubscription(subscription: any) {
    return fetch(`${apiHost}/push-subscription`, {
        method: 'POST',
        body: JSON.stringify(subscription),
        headers: {
            'Content-Type': 'application/json',
        },
    })
        .then(res => res.json())
}


export function fetchChartDataList(params: {
    symbol: string,
    timeframe: string,
    startTime?: number,
    endTime?: number,
    limit?: number
}) {
    return fetch(`${apiHost}/chart-data?symbol=${params.symbol}&timeframe=${params.timeframe}${params.limit ? `&limit=${params.limit}` : ''}${params.startTime ? `&startTime=${params.startTime}` : ''}${params.endTime ? `&endTime=${params.endTime}` : ''}`)
        .then(res => res.json())
}