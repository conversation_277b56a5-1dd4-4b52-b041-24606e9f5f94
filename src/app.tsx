import { ChartGroup } from './components/chartGroup';
import { AppContextProvider } from './contexts/appContext';
import { DataContextProvider } from './contexts/dataContext';
import { LayoutContextProvider } from './contexts/layoutContext';
import { AppContainer, BlueprintGrid, Footer, globalStyles, Header, PaperBackground } from './styles/appStyles';

import { useEffect, useRef } from 'react';
import { useSignal } from './hooks/useSignal';
import { XAxis } from './components/xAxis';
import { Selector } from './components/selector';
import { fetchSymbolList, fetchTimeframeList } from './commons/api';
import { disableDebug } from './commons/util';
import { ConcentricRect, LogoChar, LogoContainer, LogoText } from './styles/logoStyles';
disableDebug()


export const App = () => {
  console.debug('rendering app')
  globalStyles();
  const appContainer = useRef<HTMLDivElement>(null);
  const symbolOptions = useSignal<{ label: string, value: string }[]>([])
  const timeframeOptions = useSignal<{ label: string, value: string }[]>([])
  symbolOptions.use()
  timeframeOptions.use()

  const symbol = useSignal('btc')
  const timeframe = useSignal('3m')
  symbol.use()
  timeframe.use()

  useEffect(() => {
    fetchSymbolList().then(data => {
      symbolOptions.value = data.map((item: string) => ({ label: item.toUpperCase(), value: item }))
    })
    fetchTimeframeList().then(data => {
      timeframeOptions.value = data.map((item: string) => ({ label: item, value: item }))
    })
  }, [])

  return (
    <AppContextProvider
      symbol={symbol.value}
      timeframe={timeframe.value}
    >
      <DataContextProvider>
        <AppContainer ref={appContainer}>
          {/* <PaperBackground /> */}
          <BlueprintGrid />
          <LayoutContextProvider container={appContainer}>
            <ChartGroup />
            <Footer>
              <XAxis />
            </Footer>

            <Header>
              <LogoContainer>
                <ConcentricRect level={1} />
                <ConcentricRect level={2} />
                <ConcentricRect level={3} />
                <ConcentricRect level={4} />
                <LogoText>
                  {/* Create a separate LogoChar for each letter with different animation delays */}
                  <LogoChar position={0}>S</LogoChar>
                  <LogoChar position={1}>o</LogoChar>
                  <LogoChar position={2}>l</LogoChar>
                  <LogoChar position={3}>a</LogoChar>
                  <LogoChar position={4}>r</LogoChar>
                  <LogoChar position={5}>i</LogoChar>
                  <LogoChar position={6}>s</LogoChar>
                </LogoText>
              </LogoContainer>
              <Selector
                value={symbol.value}
                options={symbolOptions.value}
                onSelectChange={value => symbol.value = value}
              />
              <Selector
                value={timeframe.value}
                options={timeframeOptions.value}
                onSelectChange={value => timeframe.value = value}
              />

            </Header>
          </LayoutContextProvider>
        </AppContainer>
      </DataContextProvider>
    </AppContextProvider>
  )
}
